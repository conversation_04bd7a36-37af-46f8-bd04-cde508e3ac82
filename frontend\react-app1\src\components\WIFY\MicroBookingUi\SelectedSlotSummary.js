import React from 'react';
import { Button, Tag } from 'antd';
import moment from 'moment';

const SelectedSlotSummary = ({
    selectedSlots,
    canReschedule,
    editMode,
    onClearSlots,
}) => {
    const hasSelectedSlots = Object.values(selectedSlots).some(
        (slots) => slots.length > 0
    );

    if (!hasSelectedSlots) {
        return null;
    }

    return (
        <div className="selected-slot-summary">
            <div
                style={{
                    fontSize: '14px',
                    fontWeight: 'bold',
                    marginBottom: '12px',
                    color: '#333',
                }}
            >
                SELECTED SLOT
            </div>

            {Object.entries(selectedSlots).map(([date, slots]) => {
                if (slots.length === 0) return null;

                return (
                    <div
                        className="selected-slot-card gx-border-2 gx-border-green"
                        key={date}
                    >
                        <span className="selected-slot-day gx-d-flex gx-justify-content-between">
                            <div>
                                {moment(date).isValid()
                                    ? moment(date).format('dddd Do MMM')
                                    : date}
                            </div>
                            {canReschedule && (
                                <div>
                                    <Button
                                        type="primary"
                                        size="small"
                                        onClick={onClearSlots}
                                        style={{
                                            padding: 0,
                                        }}
                                    >
                                        {editMode ? 'reschedule' : 'clear'}
                                    </Button>
                                </div>
                            )}
                        </span>
                        <hr className="selected-slot-divider" />
                        <div className="selected-slot-tags">
                            {slots.map((slot) => (
                                <Tag
                                    key={`${date}-${slot}`}
                                    color="success"
                                    className="selected-slot-tag"
                                >
                                    {slot}
                                </Tag>
                            ))}
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default SelectedSlotSummary;
