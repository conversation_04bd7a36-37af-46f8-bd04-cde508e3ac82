import React, { useState, useRef, useEffect } from 'react';
import './aichatbot.css';
import { <PERSON><PERSON>, Drawer, Tooltip } from 'antd';
import { FiSend } from 'react-icons/fi';
import { SlArrowDownCircle } from 'react-icons/sl';
import { PiNotePencil } from 'react-icons/pi';
import { SlClose } from 'react-icons/sl';
import ConfigHelpers from '../../../util/ConfigHelpers';
import { isMobileView } from '../../../util/helpers';
import AgentMessage from './AgentMessage';
import UserMessage from './UserMessage';
import http_utils from '../../../util/http_utils';
import axios from 'axios';

const AiChatBot = ({ showAiChatBot, setShowAiChatBot }) => {
    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [sessionId, setSessionId] = useState('');
    const [isScrolledUp, setIsScrolledUp] = useState(false);

    // Refs for scroll handling
    const lastMsgInChat = useRef(null);
    const chatContainerRef = useRef(null);

    function initViewData(latestMessage) {
        console.log(
            'AiChatBot :: initViewData :: latestMessage',
            latestMessage
        );

        const newChat = messages?.length == 0;
        var params = {
            latestMessage,
            is_new_chat: newChat,
            session_id: sessionId,
        };
        const onComplete = (resp) => {
            console.log(
                'AiChatBot :: initViewData :: onComplete :: resp data',
                resp.data
            );
            setMessages((prev) =>
                prev.map((msg) =>
                    msg.isGenerating
                        ? {
                              ...msg,
                              text: resp?.data?.text,
                              isGenerating: false,
                              block: resp?.data?.message,
                          }
                        : msg
                )
            );
            setSessionId(resp?.data?.session_id || '');
            scrollToBottom();
        };
        const onError = (error) => {
            console.log('AiChatBot :: error ::', error);
        };
        http_utils.performGetCall('/ai-chatbot/', params, onComplete, onError);
    }
    // Auto-scroll on user message
    useEffect(() => {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage?.from === 'user' || lastMessage?.isGenerating) {
            const timeout = setTimeout(() => {
                scrollToBottom();
            }, 50);
            return () => clearTimeout(timeout);
        }
    }, [messages]);

    // Detect scroll position
    useEffect(() => {
        const container = chatContainerRef.current;
        const handleScroll = () => {
            const nearBottom =
                container.scrollHeight - container.scrollTop <=
                container.clientHeight + 100;
            setIsScrolledUp(!nearBottom);
        };

        if (container) {
            container.addEventListener('scroll', handleScroll);
        }
        // Check scroll position right after new message is added
        if (container) {
            const nearBottom =
                container.scrollHeight - container.scrollTop <=
                container.clientHeight + 100;
            setIsScrolledUp(!nearBottom);
        }

        return () => {
            if (container) {
                container.removeEventListener('scroll', handleScroll);
            }
        };
    }, [messages]);

    // Scroll to bottom of message list
    const scrollToBottom = () => {
        if (lastMsgInChat.current) {
            lastMsgInChat.current.scrollIntoView({
                behavior: 'smooth',
            });
        }
    };

    // Get formatted current time
    const getCurrentTime = () =>
        new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
        });

    // Handle message send
    const handleUserPrompt = () => {
        console.log('AiChatBot :: handleUserPrompt', input);
        if (!input.trim()) return;

        const time = getCurrentTime();

        const userMessage = {
            from: 'user',
            text: input,
            time,
        };

        // Add the user message to the chat immediately
        setMessages((prev) => [...prev, userMessage]);
        setInput('');
        // Show generating resp animation in chat
        const generatingMsg = {
            from: 'tms-ai',
            text: <div className="wy-acb-initial-loading"></div>,
            time,
            isGenerating: true,
        };
        setMessages((prev) => [...prev, generatingMsg]);

        initViewData(userMessage);
    };

    const handleNewChat = () => {
        setMessages([]);
        setSessionId('');
    };

    const renderChatView = () => (
        <>
            <div className="wy-acb-messages" ref={chatContainerRef}>
                {messages.map((msg, idx) => {
                    if (msg.from === 'user') {
                        return <UserMessage key={idx} message={msg} />;
                    }
                    return (
                        <AgentMessage
                            key={idx}
                            message={msg}
                            isGenerating={msg.isGenerating}
                        />
                    );
                })}
                <div ref={lastMsgInChat} />
            </div>

            {isScrolledUp && (
                <div>
                    <button
                        onClick={scrollToBottom}
                        className="wy-acb-jump-to-latest-button"
                    >
                        <SlArrowDownCircle /> Jump to Latest
                    </button>
                </div>
            )}

            {messages.length === 0 && (
                <div className="wy-acb-intro-message gx-text-center">
                    <div className="wy-acb-name">
                        Hello! {ConfigHelpers.getFullUserName()}
                    </div>{' '}
                    <div className="wy-acb-intro-message-text">
                        I am your TMS AI Assistant.
                        <br />
                        How can I help you with your TMS operations today?
                    </div>
                </div>
            )}

            <div className="wy-acb-prompt-wrapper">
                <textarea
                    placeholder="Type a message..."
                    value={input}
                    //disable text area if isGenerating is true

                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={(e) => {
                        const isGenerating = messages.some(
                            (msg) => msg.isGenerating
                        );
                        if (e.key === 'Enter' && !e.shiftKey) {
                            if (isGenerating) {
                                e.preventDefault(); // Block submit while generating
                            } else {
                                e.preventDefault(); // ✅ Prevents new line
                                handleUserPrompt();
                            }
                        }
                    }}
                    className="wy-acb-prompt-textarea"
                />
                <button
                    onClick={handleUserPrompt}
                    className="wy-acb-send-button"
                    disabled={messages.some((msg) => msg.isGenerating)}
                >
                    <FiSend />
                </button>
            </div>
        </>
    );

    return (
        <>
            <Drawer
                placement={isMobileView() ? 'bottom' : 'right'}
                visible={showAiChatBot}
                onClose={() => setShowAiChatBot(false)}
                className="wy-acb-drawer-wrapper"
                headerStyle={{ display: 'none' }}
            >
                {/* Main container */}
                <div className="wy-acb-chat-container box">
                    {/* Header with controls */}
                    <div className="wy-acb-chat-header gx-d-flex gx-justify-content-between gx-align-items-center">
                        <h2 className="gx-mb-0 wy-acb-title-ai-assistant">
                            TMS AI Assistant
                        </h2>
                        <div className="gx-d-flex gx-align-items-center">
                            <Tooltip title="Start New Chat" placement="bottom">
                                <Button
                                    size="small"
                                    onClick={handleNewChat}
                                    className="gx-mr-0 gx-mb-0 wy-acb-new-chat-button gx-fs-18 gx-d-flex gx-align-items-center"
                                >
                                    <PiNotePencil />
                                </Button>
                            </Tooltip>
                            {/* <button
                                onClick={() => setShowAiChatBot(false)}
                                className="gx-bg-transparent gx-border-0 gx-text-red gx-fs-md wy-cursor-pointer main-interface-close-button gx-d-none"
                            >
                                <SlClose />
                            </button> */}
                        </div>
                    </div>
                    {renderChatView()}
                </div>
            </Drawer>
        </>
    );
};

export default AiChatBot;
