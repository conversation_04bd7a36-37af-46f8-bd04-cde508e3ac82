CREATE OR REPLACE FUNCTION public.tms_hlpr_clear_booked_slots_from_form_data(srvc_req_id_ integer)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
    status boolean;
    message text;
    resp_data json;
    affected_rows integer;

begin
    status = false;
    message = 'Internal_error';
      
    -- Remove booked_slots from form_data
    UPDATE public.cl_tx_srvc_req 
        SET form_data = jsonb_set(form_data::jsonb - 'booked_slots' - 'start_time'- 'end_time' - 'request_req_date', '{booking_data}', 'null'::jsonb,true),
        capacity_id = NULL  ,
        booking_details = null
    WHERE db_id = srvc_req_id_;
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    if affected_rows > 0 then
        status = true;
        message = 'success';
        resp_data = json_build_object(
            'srvc_req_id', srvc_req_id_,
            'booked_slots_cleared', true
        );
    else 
        status = false;
        message = 'failed to clear booked slots';
        resp_data = json_build_object(
            'srvc_req_id', srvc_req_id_,
            'booked_slots_cleared', false
        );    
    end if;
   
    
    return json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$
;
