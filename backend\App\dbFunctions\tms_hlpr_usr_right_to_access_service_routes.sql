CREATE OR REPLACE FUNCTION public.tms_hlpr_usr_right_to_access_service_routes(user_id uuid, right_type_ text[], srvc_type_id_ text)
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
DECLARE
    roles_ids int[];
    result_ BOOLEAN default false;
begin
	
	roles_ids = tms_hlpr_get_user_role_ids(user_id);
	
	SELECT EXISTS (
        SELECT 1
        FROM cl_cf_role_access
        WHERE role_id = ANY(roles_ids)
          AND access_type = 'access_service_routes'
          AND right_type_ && rights_type::text[]
          AND menu_id = srvc_type_id_
        LIMIT 1
    ) INTO result_;
   
   return result_;
END;
$function$
;
